// Copyright © 2024-present wzw

using Microsoft.AspNetCore.Mvc.RazorPages;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YwAdmin.Multiplex.Contracts;
using YwAdmin.SqlSugar.Entity;
using YwAdmin.SqlSugar.Entity.WareHouse;

namespace YwAdmin.Core.Extensions
{
    public static class QueryableExtensions
    {
        public static ISugarQueryable<T> InputPage<T>(this ISugarQueryable<T> query, PaginationParams input)
        {
            var pageIndex = input.PageIndex <= 0 ? 1 : input.PageIndex;
            var pageSize = input.PageSize <= 0 ? 10 : input.PageSize;

            return query.Skip((pageIndex - 1) * pageSize).Take(pageSize);
        }


        public static ISugarQueryable<T> ApplyFilters<T>(this ISugarQueryable<T> query, List<FilterCondition> filters)
        {
            if (filters == null || !filters.Any()) return query;

            var andGroup = filters.Where(f => f.Operator?.ToUpper() != "OR").ToList();
            var orGroup = filters.Where(f => f.Operator?.ToUpper() == "OR").ToList();

            // andGroup 加索引传递参数名和值
            for (int i = 0; i < andGroup.Count; i++)
            {
                var filter = andGroup[i];
                var expr = BuildExpr(filter, i);
                if (expr != null)
                {
                    var paramName = $"value{i}";
                    query = query.Where(expr, new Dictionary<string, object> { { paramName, filter.Value } });
                }
            }

            if (orGroup.Any())
            {
                var orExprs = new List<string>();
                var paramMap = new Dictionary<string, object>();
                for (int i = 0; i < orGroup.Count; i++)
                {
                    var f = orGroup[i];
                    var paramName = $"@or{i}";
                    var expr = BuildExpr(f, i);
                    if (expr != null)
                    {
                        orExprs.Add(expr);
                        paramMap[paramName.TrimStart('@')] = f.Value;
                    }
                }
                if (orExprs.Any())
                {
                    var clause = "(" + string.Join(" OR ", orExprs) + ")";
                    query = query.Where(clause, paramMap);
                }
            }

            return query;
        }

        private static string? BuildExpr(FilterCondition filter, int index)
        {

            var paramName = $"@value{index}";
            return filter.FilterType switch
            {
                "equals" => $"{filter.Column} = {paramName}",
                "contains" => $"{filter.Column} LIKE '%' + {paramName} + '%'",
                "notContains" => $"{filter.Column} NOT LIKE '%' + {paramName} + '%'",
                "startsWith" => $"{filter.Column} LIKE {paramName} + '%'",
                "endsWith" => $"{filter.Column} LIKE '%' + {paramName}",
                "notBlank" => $"{filter.Column} IS NOT NULL AND LTRIM(RTRIM({filter.Column})) <> ''",
                "blank" => $"{filter.Column} IS NULL OR LTRIM(RTRIM({filter.Column})) = ''",
                _ => null
            };
        }

        public static ISugarQueryable<T> ApplySort<T>(this ISugarQueryable<T> query, List<SortModel> sortModel)
        {
            if (sortModel == null || !sortModel.Any()) return query;

            // 按优先级排序
            var orderedSorts = sortModel.OrderBy(s => s.Priority).ToList();

            ISugarQueryable<T> sortedQuery = query;
            bool isFirst = true;

            foreach (var sort in orderedSorts)
            {
                if (string.IsNullOrEmpty(sort.Field)) continue;

                var direction = sort.Direction?.ToLower() == "desc" ? "desc" : "asc";
                
                if (isFirst)
                {
                    sortedQuery = direction == "desc" 
                        ? sortedQuery.OrderBy($"{sort.Field} desc")
                        : sortedQuery.OrderBy(sort.Field);
                    isFirst = false;
                }
                else
                {
                    sortedQuery = direction == "desc"
                        ? sortedQuery.OrderBy($"{sort.Field} desc")
                        : sortedQuery.OrderBy(sort.Field);
                }
            }

            return sortedQuery;
        }

        // 联表查询结果包装器扩展方法
        public static ISugarQueryable<JoinResult<T1, T2>> ToJoinResult<T1, T2>(this ISugarQueryable<T1, T2> query)
        {
            return query.Select((t1, t2) => new JoinResult<T1, T2> { Table1 = t1, Table2 = t2 });
        }

        // 为 Inbound 和 UserEntity 创建特定的包装器
        public static ISugarQueryable<InboundUserJoin> ToInboundUserJoin(this ISugarQueryable<Inbound, UserEntity> query)
        {
            return query.Select((inb, user) => new InboundUserJoin { inbound = inb, user = user });
        }

        public static ISugarQueryable<JoinResult<T1, T2, T3>> ToJoinResult<T1, T2, T3>(this ISugarQueryable<T1, T2, T3> query)
        {
            return query.Select((t1, t2, t3) => new JoinResult<T1, T2, T3> { Table1 = t1, Table2 = t2, Table3 = t3 });
        }

        public static ISugarQueryable<JoinResult<T1, T2, T3, T4>> ToJoinResult<T1, T2, T3, T4>(this ISugarQueryable<T1, T2, T3, T4> query)
        {
            return query.Select((t1, t2, t3, t4) => new JoinResult<T1, T2, T3, T4> { Table1 = t1, Table2 = t2, Table3 = t3, Table4 = t4 });
        }

        public static ISugarQueryable<JoinResult<T1, T2, T3, T4, T5>> ToJoinResult<T1, T2, T3, T4, T5>(this ISugarQueryable<T1, T2, T3, T4, T5> query)
        {
            return query.Select((t1, t2, t3, t4, t5) => new JoinResult<T1, T2, T3, T4, T5> { Table1 = t1, Table2 = t2, Table3 = t3, Table4 = t4, Table5 = t5 });
        }
    }

    // 联表查询结果包装类
    public class JoinResult<T1, T2>
    {
        public T1 Table1 { get; set; }
        public T2 Table2 { get; set; }
    }

    public class JoinResult<T1, T2, T3>
    {
        public T1 Table1 { get; set; }
        public T2 Table2 { get; set; }
        public T3 Table3 { get; set; }
    }

    public class JoinResult<T1, T2, T3, T4>
    {
        public T1 Table1 { get; set; }
        public T2 Table2 { get; set; }
        public T3 Table3 { get; set; }
        public T4 Table4 { get; set; }
    }

    public class JoinResult<T1, T2, T3, T4, T5>
    {
        public T1 Table1 { get; set; }
        public T2 Table2 { get; set; }
        public T3 Table3 { get; set; }
        public T4 Table4 { get; set; }
        public T5 Table5 { get; set; }
    }
}

