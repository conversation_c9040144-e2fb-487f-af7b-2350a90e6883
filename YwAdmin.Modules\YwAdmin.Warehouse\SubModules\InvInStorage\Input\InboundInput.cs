// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YwAdmin.Multiplex.Contracts;

namespace YwAdmin.Warehouse.InvInStorage.Input;

/// <summary>
/// Inbound表输入模型
/// </summary>
public class InboundInput : PaginationParams
{
    /// <summary>
    /// ID
    /// </summary>
    public long? Id { get; set; }

	/// <summary>
	/// 入库单号
	/// </summary>
	public string ReceiptNo { get; set; }
	/// <summary>
	/// 入库时间
	/// </summary>
	public DateTime? Receiptdate { get; set; }
	/// <summary>
	/// 供应商代码
	/// </summary>
	public string Supplierno { get; set; }
	/// <summary>
	/// 类型
	/// </summary>
	public string Invtype { get; set; }
	/// <summary>
	/// 订单号
	/// </summary>
	public string Orderno { get; set; }
	/// <summary>
	/// 仓库代码
	/// </summary>
	public string Warehousecode { get; set; }

    /// <summary>
    /// 字段前缀映射，用于多表联查时避免字段歧义
    /// </summary>
    protected override Dictionary<string, string> PrefixMap { get; } = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
    {
        // Inbound 表字段映射
        { "Id", "inb" },
        { "ReceiptNo", "inb" },
        { "ReceiptDate", "inb" },
        { "SupplierNo", "inb" },
        { "InvType", "inb" },
        { "OrderNo", "inb" },
        { "WarehouseCode", "inb" },
        { "CreateUserId", "inb" },
        { "CreateTime", "inb" },
        { "UpdateUserId", "inb" },
        { "UpdateTime", "inb" },

        // UserEntity 表字段映射
        { "Account", "user" },
        { "Name", "user" },
        { "Telephone", "user" },
        { "Email", "user" }
    };
}
